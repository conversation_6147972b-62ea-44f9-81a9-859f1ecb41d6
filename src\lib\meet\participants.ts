import { getMeetClient } from './client'
import { Participant, ParticipantSession, ListResponse } from './types'

/**
 * Get a specific participant
 */
export async function getParticipant(
  userId: string,
  participantName: string
): Promise<Participant> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getParticipant({
    name: participantName
  })
  
  return response as Participant
}

/**
 * List participants in a conference
 */
export async function listParticipants(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: string
  } = {}
): Promise<ListResponse<Participant>> {
  const { meetClient } = await getMeetClient(userId)
  
  const request: any = {
    parent: conferenceRecordName,
    pageSize: options.pageSize || 10
  }
  
  if (options.pageToken) {
    request.pageToken = options.pageToken
  }
  
  if (options.filter) {
    request.filter = options.filter
  }
  
  const [response] = await meetClient.listParticipants(request)
  
  return {
    items: (response.participants || []) as Participant[],
    nextPageToken: response.nextPageToken,
    totalSize: response.participants?.length
  }
}

/**
 * Get a specific participant session
 */
export async function getParticipantSession(
  userId: string,
  participantSessionName: string
): Promise<ParticipantSession> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getParticipantSession({
    name: participantSessionName
  })
  
  return response as ParticipantSession
}

/**
 * List participant sessions for a participant
 */
export async function listParticipantSessions(
  userId: string,
  participantName: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: string
  } = {}
): Promise<ListResponse<ParticipantSession>> {
  const { meetClient } = await getMeetClient(userId)
  
  const request: any = {
    parent: participantName,
    pageSize: options.pageSize || 10
  }
  
  if (options.pageToken) {
    request.pageToken = options.pageToken
  }
  
  if (options.filter) {
    request.filter = options.filter
  }
  
  const [response] = await meetClient.listParticipantSessions(request)
  
  return {
    items: (response.participantSessions || []) as ParticipantSession[],
    nextPageToken: response.nextPageToken,
    totalSize: response.participantSessions?.length
  }
}

/**
 * Get all participants with their sessions for a conference
 */
export async function getConferenceParticipants(
  userId: string,
  conferenceRecordName: string,
  includeSessions: boolean = false
): Promise<{
  participants: Participant[]
  totalParticipants: number
  participantSessions?: { [participantName: string]: ParticipantSession[] }
}> {
  const participantsResponse = await listParticipants(userId, conferenceRecordName, {
    pageSize: 100 // Get all participants
  })
  
  const result: any = {
    participants: participantsResponse.items,
    totalParticipants: participantsResponse.items.length
  }
  
  if (includeSessions) {
    const participantSessions: { [participantName: string]: ParticipantSession[] } = {}
    
    // Get sessions for each participant
    for (const participant of participantsResponse.items) {
      try {
        const sessionsResponse = await listParticipantSessions(userId, participant.name, {
          pageSize: 50
        })
        participantSessions[participant.name] = sessionsResponse.items
      } catch (error) {
        console.error(`Error getting sessions for participant ${participant.name}:`, error)
        participantSessions[participant.name] = []
      }
    }
    
    result.participantSessions = participantSessions
  }
  
  return result
}

/**
 * Get participant statistics for a conference
 */
export async function getParticipantStatistics(
  userId: string,
  conferenceRecordName: string
): Promise<{
  totalParticipants: number
  totalSessions: number
  averageSessionsPerParticipant: number
  participantTypes: {
    authenticated: number
    anonymous: number
    phone: number
  }
  participationDuration: {
    total: number // in minutes
    average: number // in minutes
    longest: number // in minutes
    shortest: number // in minutes
  }
}> {
  const { participants } = await getConferenceParticipants(userId, conferenceRecordName, true)
  
  let totalSessions = 0
  let authenticatedCount = 0
  let anonymousCount = 0
  let phoneCount = 0
  let totalDuration = 0
  let durations: number[] = []
  
  for (const participant of participants) {
    // Count participant types
    if (participant.phoneNumber) {
      phoneCount++
    } else if (participant.anonymousUser) {
      anonymousCount++
    } else {
      authenticatedCount++
    }
    
    // Calculate participation duration
    if (participant.earliestStartTime && participant.latestEndTime) {
      const duration = (new Date(participant.latestEndTime).getTime() - 
                       new Date(participant.earliestStartTime).getTime()) / (1000 * 60)
      totalDuration += duration
      durations.push(duration)
    }
  }
  
  return {
    totalParticipants: participants.length,
    totalSessions,
    averageSessionsPerParticipant: participants.length > 0 ? totalSessions / participants.length : 0,
    participantTypes: {
      authenticated: authenticatedCount,
      anonymous: anonymousCount,
      phone: phoneCount
    },
    participationDuration: {
      total: totalDuration,
      average: durations.length > 0 ? totalDuration / durations.length : 0,
      longest: durations.length > 0 ? Math.max(...durations) : 0,
      shortest: durations.length > 0 ? Math.min(...durations) : 0
    }
  }
}

/**
 * Get participant attendance summary
 */
export async function getParticipantAttendance(
  userId: string,
  conferenceRecordName: string
): Promise<{
  participantName: string
  displayName?: string
  joinTime: string
  leaveTime?: string
  duration: number // in minutes
  sessionCount: number
  isAnonymous: boolean
  isPhoneUser: boolean
}[]> {
  const { participants, participantSessions } = await getConferenceParticipants(
    userId, 
    conferenceRecordName, 
    true
  )
  
  const attendance = participants.map(participant => {
    const sessions = participantSessions?.[participant.name] || []
    const sessionCount = sessions.length
    
    // Calculate total duration
    let duration = 0
    if (participant.earliestStartTime && participant.latestEndTime) {
      duration = (new Date(participant.latestEndTime).getTime() - 
                 new Date(participant.earliestStartTime).getTime()) / (1000 * 60)
    }
    
    return {
      participantName: participant.name,
      displayName: participant.anonymousUser?.displayName,
      joinTime: participant.earliestStartTime,
      leaveTime: participant.latestEndTime,
      duration,
      sessionCount,
      isAnonymous: !!participant.anonymousUser,
      isPhoneUser: !!participant.phoneNumber
    }
  })
  
  // Sort by join time
  return attendance.sort((a, b) => 
    new Date(a.joinTime).getTime() - new Date(b.joinTime).getTime()
  )
}

/**
 * Search participants by criteria
 */
export async function searchParticipants(
  userId: string,
  conferenceRecordName: string,
  criteria: {
    displayName?: string
    isAnonymous?: boolean
    isPhoneUser?: boolean
    minDuration?: number // in minutes
  }
): Promise<Participant[]> {
  const { participants } = await getConferenceParticipants(userId, conferenceRecordName)
  
  return participants.filter(participant => {
    // Filter by display name
    if (criteria.displayName) {
      const displayName = participant.anonymousUser?.displayName || ''
      if (!displayName.toLowerCase().includes(criteria.displayName.toLowerCase())) {
        return false
      }
    }
    
    // Filter by anonymous status
    if (criteria.isAnonymous !== undefined) {
      const isAnonymous = !!participant.anonymousUser
      if (isAnonymous !== criteria.isAnonymous) {
        return false
      }
    }
    
    // Filter by phone user status
    if (criteria.isPhoneUser !== undefined) {
      const isPhoneUser = !!participant.phoneNumber
      if (isPhoneUser !== criteria.isPhoneUser) {
        return false
      }
    }
    
    // Filter by minimum duration
    if (criteria.minDuration !== undefined) {
      if (participant.earliestStartTime && participant.latestEndTime) {
        const duration = (new Date(participant.latestEndTime).getTime() - 
                         new Date(participant.earliestStartTime).getTime()) / (1000 * 60)
        if (duration < criteria.minDuration) {
          return false
        }
      } else {
        return false // No duration data available
      }
    }
    
    return true
  })
}
