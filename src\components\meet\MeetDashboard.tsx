'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Video,
  Users,
  Clock,
  FileText,
  Download,
  TrendingUp,
  Activity,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  ExternalLink,
  Play,
  Mic,
  Settings,
  Filter,
  Plus
} from 'lucide-react'

interface MeetDashboardProps {
  className?: string
}

interface DashboardData {
  recentMeetings: any[]
  statistics: {
    totalConferences: number
    totalDuration: number
    averageDuration: number
    totalParticipants: number
    averageParticipants: number
    conferencesWithRecordings: number
    conferencesWithTranscripts: number
    peakParticipants: number
    totalMeetingHours: number
    averageMeetingLength: number
    meetingsThisWeek: number
    meetingsLastWeek: number
    growthRate: number
  }
  upcomingMeetings: any[]
  totalArtifacts: {
    recordings: number
    transcripts: number
    totalSize: number
    downloadableRecordings: number
  }
  analytics: {
    dailyMeetings: Array<{ date: string; count: number }>
    participantTrends: Array<{ date: string; participants: number }>
    durationTrends: Array<{ date: string; duration: number }>
    topParticipants: Array<{ email: string; meetingCount: number }>
    meetingTypes: Array<{ type: string; count: number }>
  }
  insights: {
    mostActiveDay: string
    averageJoinTime: number
    commonMeetingDuration: number
    recordingUsage: number
    transcriptUsage: number
  }
}

export function MeetDashboard({ className }: MeetDashboardProps) {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [refreshing, setRefreshing] = useState(false)
  const [selectedTab, setSelectedTab] = useState('overview')
  const [filterType, setFilterType] = useState('all')

  useEffect(() => {
    fetchDashboardData()
  }, [selectedPeriod])

  const fetchDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }
      const response = await fetch(`/api/meet/dashboard?days=${selectedPeriod}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch dashboard data')
      }

      const result = await response.json()
      setDashboardData(result.data)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      console.error('Dashboard fetch error:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    fetchDashboardData(true)
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const formatGrowthRate = (rate: number) => {
    const sign = rate >= 0 ? '+' : ''
    return `${sign}${rate.toFixed(1)}%`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => fetchDashboardData()} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!dashboardData) {
    return null
  }

  const { statistics, recentMeetings, totalArtifacts } = dashboardData

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Activity className="h-8 w-8 text-blue-600" />
            Meet Dashboard
          </h1>
          <p className="text-muted-foreground">
            Overview of your Google Meet activity for the last {selectedPeriod} days
          </p>
        </div>
        <div className="flex gap-2 items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <div className="flex gap-1">
            {['7', '30', '90'].map((days) => (
              <Button
                key={days}
                variant={selectedPeriod === days ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(days)}
              >
                {days} days
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Meetings</CardTitle>
            <Video className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalConferences}</div>
            <p className="text-xs text-muted-foreground">
              Last {selectedPeriod} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(statistics.totalDuration)}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatDuration(statistics.averageDuration)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalParticipants}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {Math.round(statistics.averageParticipants)} per meeting
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recordings</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalArtifacts.recordings}</div>
            <p className="text-xs text-muted-foreground">
              {totalArtifacts.transcripts} transcripts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center gap-2">
              {statistics.growthRate !== undefined ? (
                <>
                  <span className={statistics.growthRate >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {formatGrowthRate(statistics.growthRate)}
                  </span>
                  <TrendingUp className={`h-4 w-4 ${statistics.growthRate >= 0 ? 'text-green-600' : 'text-red-600 rotate-180'}`} />
                </>
              ) : (
                <span className="text-muted-foreground">N/A</span>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              vs previous period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="recent">Recent Meetings</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="artifacts">Artifacts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Quick Statistics
                </CardTitle>
                <CardDescription>
                  Key metrics at a glance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Peak Participants</span>
                  <span className="font-semibold">{statistics.peakParticipants || 'N/A'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Meeting Hours</span>
                  <span className="font-semibold">{Math.round((statistics.totalMeetingHours || 0) * 10) / 10}h</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Avg Meeting Length</span>
                  <span className="font-semibold">{formatDuration(statistics.averageMeetingLength || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">This Week</span>
                  <span className="font-semibold">{statistics.meetingsThisWeek || 0} meetings</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Last Week</span>
                  <span className="font-semibold">{statistics.meetingsLastWeek || 0} meetings</span>
                </div>
              </CardContent>
            </Card>

            {/* Usage Insights */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Usage Insights
                </CardTitle>
                <CardDescription>
                  Meeting patterns and trends
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {dashboardData.insights ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Most Active Day</span>
                      <span className="font-semibold">{dashboardData.insights.mostActiveDay}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Avg Join Time</span>
                      <span className="font-semibold">{dashboardData.insights.averageJoinTime}s</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Common Duration</span>
                      <span className="font-semibold">{formatDuration(dashboardData.insights.commonMeetingDuration)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Recording Usage</span>
                      <span className="font-semibold">{dashboardData.insights.recordingUsage}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Transcript Usage</span>
                      <span className="font-semibold">{dashboardData.insights.transcriptUsage}%</span>
                    </div>
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">No insights available yet</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Meetings
                </CardTitle>
                <CardDescription>
                  Your latest meeting activity for the last {selectedPeriod} days
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {recentMeetings.length === 0 ? (
                <div className="text-center py-12">
                  <Video className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No recent meetings</h3>
                  <p className="text-muted-foreground mb-4">
                    You haven't had any meetings in the last {selectedPeriod} days.
                  </p>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Create Meeting Space
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {recentMeetings.slice(0, 8).map((meeting, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <Video className="h-5 w-5 text-blue-500" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium truncate">
                            Meeting {meeting.name?.split('/').pop() || 'Unknown'}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatDate(meeting.startTime)}
                            </span>
                            {meeting.participantCount && (
                              <span className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {meeting.participantCount} participants
                              </span>
                            )}
                            {meeting.duration && (
                              <span>Duration: {formatDuration(meeting.duration)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={meeting.endTime ? "secondary" : "default"}>
                          {meeting.endTime ? 'Completed' : 'In Progress'}
                        </Badge>
                        {meeting.hasRecording && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Play className="h-3 w-3" />
                            Recording
                          </Badge>
                        )}
                        {meeting.hasTranscript && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Mic className="h-3 w-3" />
                            Transcript
                          </Badge>
                        )}
                        <Button variant="ghost" size="sm" className="flex items-center gap-1">
                          <ExternalLink className="h-3 w-3" />
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                  {recentMeetings.length > 8 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" size="sm">
                        View All {recentMeetings.length} Meetings
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Meeting Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Meetings with Recordings</span>
                    <span className="font-medium">{statistics.conferencesWithRecordings}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Meetings with Transcripts</span>
                    <span className="font-medium">{statistics.conferencesWithTranscripts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Duration</span>
                    <span className="font-medium">{formatDuration(statistics.averageDuration)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Activity Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {statistics.totalConferences}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Meetings</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {formatDuration(statistics.totalDuration)}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Time</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="artifacts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recordings & Transcripts</CardTitle>
              <CardDescription>
                Access your meeting recordings and transcripts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center p-6 border rounded-lg">
                  <Download className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                  <div className="text-2xl font-bold">{totalArtifacts.recordings}</div>
                  <p className="text-sm text-muted-foreground">Recordings Available</p>
                </div>
                <div className="text-center p-6 border rounded-lg">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  <div className="text-2xl font-bold">{totalArtifacts.transcripts}</div>
                  <p className="text-sm text-muted-foreground">Transcripts Available</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
