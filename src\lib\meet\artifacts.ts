import { getMeetClient } from './client'
import { Recording, Transcript, TranscriptEntry, ListResponse } from './types'

/**
 * Get a specific recording
 */
export async function getRecording(
  userId: string,
  recordingName: string
): Promise<Recording> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getRecording({
    name: recordingName
  })
  
  return response as Recording
}

/**
 * List recordings for a conference
 */
export async function listRecordings(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<Recording>> {
  const { meetClient } = await getMeetClient(userId)
  
  const request: any = {
    parent: conferenceRecordName,
    pageSize: options.pageSize || 10
  }
  
  if (options.pageToken) {
    request.pageToken = options.pageToken
  }
  
  const [response] = await meetClient.listRecordings(request)
  
  return {
    items: (response.recordings || []) as Recording[],
    nextPageToken: response.nextPageToken,
    totalSize: response.recordings?.length
  }
}

/**
 * Get a specific transcript
 */
export async function getTranscript(
  userId: string,
  transcriptName: string
): Promise<Transcript> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getTranscript({
    name: transcriptName
  })
  
  return response as Transcript
}

/**
 * List transcripts for a conference
 */
export async function listTranscripts(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<Transcript>> {
  const { meetClient } = await getMeetClient(userId)
  
  const request: any = {
    parent: conferenceRecordName,
    pageSize: options.pageSize || 10
  }
  
  if (options.pageToken) {
    request.pageToken = options.pageToken
  }
  
  const [response] = await meetClient.listTranscripts(request)
  
  return {
    items: (response.transcripts || []) as Transcript[],
    nextPageToken: response.nextPageToken,
    totalSize: response.transcripts?.length
  }
}

/**
 * Get a specific transcript entry
 */
export async function getTranscriptEntry(
  userId: string,
  transcriptEntryName: string
): Promise<TranscriptEntry> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getTranscriptEntry({
    name: transcriptEntryName
  })
  
  return response as TranscriptEntry
}

/**
 * List transcript entries for a transcript
 */
export async function listTranscriptEntries(
  userId: string,
  transcriptName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<TranscriptEntry>> {
  const { meetClient } = await getMeetClient(userId)
  
  const request: any = {
    parent: transcriptName,
    pageSize: options.pageSize || 50 // Higher default for transcript entries
  }
  
  if (options.pageToken) {
    request.pageToken = options.pageToken
  }
  
  const [response] = await meetClient.listTranscriptEntries(request)
  
  return {
    items: (response.transcriptEntries || []) as TranscriptEntry[],
    nextPageToken: response.nextPageToken,
    totalSize: response.transcriptEntries?.length
  }
}

/**
 * Get all artifacts (recordings and transcripts) for a conference
 */
export async function getConferenceArtifacts(
  userId: string,
  conferenceRecordName: string
): Promise<{
  recordings: Recording[]
  transcripts: Transcript[]
  hasRecordings: boolean
  hasTranscripts: boolean
}> {
  const [recordingsResponse, transcriptsResponse] = await Promise.all([
    listRecordings(userId, conferenceRecordName, { pageSize: 50 }),
    listTranscripts(userId, conferenceRecordName, { pageSize: 50 })
  ])
  
  return {
    recordings: recordingsResponse.items,
    transcripts: transcriptsResponse.items,
    hasRecordings: recordingsResponse.items.length > 0,
    hasTranscripts: transcriptsResponse.items.length > 0
  }
}

/**
 * Get recording download information
 */
export async function getRecordingDownloadInfo(
  userId: string,
  recordingName: string
): Promise<{
  downloadUrl: string
  fileName: string
  fileSize?: number
  duration?: number
  format?: string
}> {
  const recording = await getRecording(userId, recordingName)
  
  return {
    downloadUrl: recording.driveDestination.exportUri,
    fileName: `recording-${recording.name.split('/').pop()}.mp4`,
    // Additional metadata would be extracted from the recording object
    // if available in the API response
  }
}

/**
 * Get transcript download information
 */
export async function getTranscriptDownloadInfo(
  userId: string,
  transcriptName: string
): Promise<{
  downloadUrl: string
  fileName: string
  format: 'docs' | 'txt' | 'srt'
}> {
  const transcript = await getTranscript(userId, transcriptName)
  
  return {
    downloadUrl: transcript.docsDestination.exportUri,
    fileName: `transcript-${transcript.name.split('/').pop()}.txt`,
    format: 'docs' // Default format from Google Docs
  }
}

/**
 * Get full transcript text
 */
export async function getFullTranscriptText(
  userId: string,
  transcriptName: string
): Promise<{
  fullText: string
  entries: TranscriptEntry[]
  speakers: string[]
  duration: number // in minutes
}> {
  const entriesResponse = await listTranscriptEntries(userId, transcriptName, {
    pageSize: 1000 // Get all entries
  })
  
  const entries = entriesResponse.items
  const speakers = [...new Set(entries.map(entry => entry.participant))]
  
  // Build full text
  const fullText = entries
    .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
    .map(entry => `${entry.participant}: ${entry.text}`)
    .join('\n')
  
  // Calculate duration
  let duration = 0
  if (entries.length > 0) {
    const firstEntry = entries[0]
    const lastEntry = entries[entries.length - 1]
    duration = (new Date(lastEntry.endTime).getTime() - new Date(firstEntry.startTime).getTime()) / (1000 * 60)
  }
  
  return {
    fullText,
    entries,
    speakers,
    duration
  }
}

/**
 * Search transcript entries by text
 */
export async function searchTranscriptEntries(
  userId: string,
  transcriptName: string,
  searchQuery: string,
  options: {
    caseSensitive?: boolean
    wholeWords?: boolean
  } = {}
): Promise<{
  matches: (TranscriptEntry & { matchIndex: number })[]
  totalMatches: number
}> {
  const { entries } = await getFullTranscriptText(userId, transcriptName)
  
  const searchRegex = new RegExp(
    options.wholeWords ? `\\b${searchQuery}\\b` : searchQuery,
    options.caseSensitive ? 'g' : 'gi'
  )
  
  const matches: (TranscriptEntry & { matchIndex: number })[] = []
  
  entries.forEach((entry, index) => {
    if (searchRegex.test(entry.text)) {
      matches.push({
        ...entry,
        matchIndex: index
      })
    }
  })
  
  return {
    matches,
    totalMatches: matches.length
  }
}

/**
 * Get artifacts summary for multiple conferences
 */
export async function getArtifactsSummary(
  userId: string,
  conferenceRecordNames: string[]
): Promise<{
  totalRecordings: number
  totalTranscripts: number
  conferencesWithRecordings: number
  conferencesWithTranscripts: number
  conferencesWithBoth: number
}> {
  const results = await Promise.all(
    conferenceRecordNames.map(name => getConferenceArtifacts(userId, name))
  )
  
  let totalRecordings = 0
  let totalTranscripts = 0
  let conferencesWithRecordings = 0
  let conferencesWithTranscripts = 0
  let conferencesWithBoth = 0
  
  results.forEach(result => {
    totalRecordings += result.recordings.length
    totalTranscripts += result.transcripts.length
    
    if (result.hasRecordings) conferencesWithRecordings++
    if (result.hasTranscripts) conferencesWithTranscripts++
    if (result.hasRecordings && result.hasTranscripts) conferencesWithBoth++
  })
  
  return {
    totalRecordings,
    totalTranscripts,
    conferencesWithRecordings,
    conferencesWithTranscripts,
    conferencesWithBoth
  }
}
