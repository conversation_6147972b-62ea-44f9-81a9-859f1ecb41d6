import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { testMeetConnection } from "@/lib/meet/client"

/**
 * GET /api/meet/status - Check Google Meet API connection status
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database with Meet info
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Test the Meet API connection only if user is connected
    let connectionTest: { connected: boolean; error?: string } = { connected: false, error: "Not connected" }
    if (user.meetConnected && user.meetRefreshToken) {
      connectionTest = await testMeetConnection(user.id)
    }

    const status = {
      userId: user.id,
      userEmail: user.email,
      meetConnected: user.meetConnected,
      hasRefreshToken: !!user.meetRefreshToken,
      connectionTest,
      timestamp: new Date().toISOString(),
      apiVersion: 'v2',
      availableFeatures: [
        'Meeting Spaces Management',
        'Conference Records Access',
        'Participant Tracking',
        'Recording Management',
        'Transcript Access',
        'Real-time Event Subscriptions',
        'Meeting Analytics',
        'Access Control Settings',
        'Entry Point Management',
        'Meeting Artifacts Download'
      ]
    }

    console.log("Meet Status Check:", {
      userId: user.id,
      email: user.email,
      meetConnected: user.meetConnected,
      hasRefreshToken: !!user.meetRefreshToken,
      connectionTest
    })

    return NextResponse.json({
      success: true,
      data: status,
      message: connectionTest.connected
        ? "Google Meet API connection is working properly"
        : user.meetConnected
          ? "Google Meet is connected but API test failed"
          : "Google Meet is not connected"
    })
  } catch (error) {
    console.error("Error checking Meet status:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
