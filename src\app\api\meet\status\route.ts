import { NextRequest, NextResponse } from "next/server"
import { 
  authenticate<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  handleMeetApiError, 
  formatMeetApiResponse,
  testMeetConnection
} from "@/lib/meet"

/**
 * GET /api/meet/status - Check Google Meet API connection status
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user } = authResult.data

    // Test the Meet API connection
    const connectionTest = await testMeetConnection(user.id)

    const status = {
      userId: user.id,
      userEmail: user.email,
      meetConnected: user.meetConnected,
      hasRefreshToken: !!user.meetRefreshToken,
      connectionTest,
      timestamp: new Date().toISOString(),
      apiVersion: 'v2',
      availableFeatures: [
        'Meeting Spaces Management',
        'Conference Records Access',
        'Participant Tracking',
        'Recording Management',
        'Transcript Access',
        'Real-time Event Subscriptions',
        'Meeting Analytics',
        'Access Control Settings',
        'Entry Point Management',
        'Meeting Artifacts Download'
      ]
    }

    return formatMeetApiResponse(
      status,
      connectionTest.connected 
        ? "Google Meet API connection is working properly"
        : "Google Meet API connection has issues"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
