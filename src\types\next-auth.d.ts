import "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
      gmailConnected?: boolean
      calendarConnected?: boolean
    }
  }

  interface User {
    id: string
    email: string
    name?: string | null
    image?: string | null
    gmailConnected?: boolean
    calendarConnected?: boolean
  }
} 