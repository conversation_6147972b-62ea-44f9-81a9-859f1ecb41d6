import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    // Check if user already has Meet connected
    const { testMeetConnection } = await import('@/lib/meet/client')
    const connectionStatus = await testMeetConnection(session.user.id)
    
    if (connectionStatus.connected) {
      return NextResponse.redirect(new URL('/dashboard/meet', request.url))
    }

    // Redirect to Google OAuth with Meet scopes
    const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')
    googleAuthUrl.searchParams.append('client_id', process.env.GOOGLE_CLIENT_ID!)
    googleAuthUrl.searchParams.append('redirect_uri', `${process.env.NEXTAUTH_URL}/api/meet/callback`)
    googleAuthUrl.searchParams.append('response_type', 'code')
    googleAuthUrl.searchParams.append('scope', 'https://www.googleapis.com/auth/meetings.space.created https://www.googleapis.com/auth/meetings.space.readonly')
    googleAuthUrl.searchParams.append('access_type', 'offline')
    googleAuthUrl.searchParams.append('prompt', 'consent')
    googleAuthUrl.searchParams.append('state', session.user.id)

    return NextResponse.redirect(googleAuthUrl.toString())

  } catch (error) {
    console.error('Meet connect error:', error)
    return NextResponse.redirect(new URL('/dashboard/meet?error=connect_failed', request.url))
  }
}
