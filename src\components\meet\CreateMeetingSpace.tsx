'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Copy, ExternalLink, Video, Settings, Users, Lock } from 'lucide-react'
import { toast } from 'sonner'

interface CreateMeetingSpaceProps {
  onMeetingCreated?: (meetingSpace: any) => void
  className?: string
}

interface MeetingSpace {
  name: string
  meetingUri: string
  meetingCode: string
  config?: {
    entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
    accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
  }
}

export function CreateMeetingSpace({ onMeetingCreated, className }: CreateMeetingSpaceProps) {
  const [loading, setLoading] = useState(false)
  const [meetingSpace, setMeetingSpace] = useState<MeetingSpace | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [accessType, setAccessType] = useState<'OPEN' | 'TRUSTED' | 'RESTRICTED'>('OPEN')
  const [entryPointAccess, setEntryPointAccess] = useState<'ALL' | 'CREATOR_APP_ONLY'>('ALL')

  const createMeetingSpace = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/meet/spaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: {
            accessType,
            entryPointAccess
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create meeting space')
      }

      const result = await response.json()
      const newMeetingSpace = result.data
      
      setMeetingSpace(newMeetingSpace)
      onMeetingCreated?.(newMeetingSpace)
      
      toast.success('Meeting space created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success(`${label} copied to clipboard!`)
    } catch (err) {
      toast.error('Failed to copy to clipboard')
    }
  }

  const openMeeting = () => {
    if (meetingSpace?.meetingUri) {
      window.open(meetingSpace.meetingUri, '_blank')
    }
  }

  const resetForm = () => {
    setMeetingSpace(null)
    setError(null)
    setAccessType('OPEN')
    setEntryPointAccess('ALL')
  }

  const getAccessTypeDescription = (type: string) => {
    switch (type) {
      case 'OPEN':
        return 'Anyone with the link can join'
      case 'TRUSTED':
        return 'Only people in your organization can join'
      case 'RESTRICTED':
        return 'Only invited participants can join'
      default:
        return ''
    }
  }

  const getEntryPointDescription = (type: string) => {
    switch (type) {
      case 'ALL':
        return 'All entry points are enabled'
      case 'CREATOR_APP_ONLY':
        return 'Only the creator app can join'
      default:
        return ''
    }
  }

  if (meetingSpace) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5 text-green-600" />
            Meeting Space Created
          </CardTitle>
          <CardDescription>
            Your meeting space is ready to use
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Meeting Link */}
          <div className="space-y-2">
            <Label>Meeting Link</Label>
            <div className="flex gap-2">
              <Input 
                value={meetingSpace.meetingUri} 
                readOnly 
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(meetingSpace.meetingUri, 'Meeting link')}
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={openMeeting}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Meeting Code */}
          <div className="space-y-2">
            <Label>Meeting Code</Label>
            <div className="flex gap-2">
              <Input 
                value={meetingSpace.meetingCode} 
                readOnly 
                className="font-mono text-lg font-bold text-center"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(meetingSpace.meetingCode, 'Meeting code')}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Configuration */}
          <div className="space-y-2">
            <Label>Configuration</Label>
            <div className="flex gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Lock className="h-3 w-3" />
                {meetingSpace.config?.accessType || 'OPEN'}
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                {meetingSpace.config?.entryPointAccess || 'ALL'}
              </Badge>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button onClick={openMeeting} className="flex-1">
              <ExternalLink className="h-4 w-4 mr-2" />
              Join Meeting
            </Button>
            <Button variant="outline" onClick={resetForm}>
              Create Another
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          Create Meeting Space
        </CardTitle>
        <CardDescription>
          Create a new Google Meet space with custom settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Access Type */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            Access Type
          </Label>
          <Select value={accessType} onValueChange={(value: any) => setAccessType(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="OPEN">
                <div>
                  <div className="font-medium">Open</div>
                  <div className="text-sm text-muted-foreground">
                    Anyone with the link can join
                  </div>
                </div>
              </SelectItem>
              <SelectItem value="TRUSTED">
                <div>
                  <div className="font-medium">Trusted</div>
                  <div className="text-sm text-muted-foreground">
                    Only people in your organization
                  </div>
                </div>
              </SelectItem>
              <SelectItem value="RESTRICTED">
                <div>
                  <div className="font-medium">Restricted</div>
                  <div className="text-sm text-muted-foreground">
                    Only invited participants
                  </div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            {getAccessTypeDescription(accessType)}
          </p>
        </div>

        {/* Entry Point Access */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Entry Point Access
          </Label>
          <Select value={entryPointAccess} onValueChange={(value: any) => setEntryPointAccess(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">
                <div>
                  <div className="font-medium">All Entry Points</div>
                  <div className="text-sm text-muted-foreground">
                    Web, mobile, phone, etc.
                  </div>
                </div>
              </SelectItem>
              <SelectItem value="CREATOR_APP_ONLY">
                <div>
                  <div className="font-medium">Creator App Only</div>
                  <div className="text-sm text-muted-foreground">
                    Only through the creator application
                  </div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            {getEntryPointDescription(entryPointAccess)}
          </p>
        </div>

        {/* Create Button */}
        <Button 
          onClick={createMeetingSpace} 
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating Meeting Space...
            </>
          ) : (
            <>
              <Video className="h-4 w-4 mr-2" />
              Create Meeting Space
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
