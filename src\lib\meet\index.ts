// Main Meet API service - exports all Meet functionality

// Client and utilities
export { getMeetClient, getSpacesClient, testMeetConnection, makeMeetApiRequest } from './client'
export { 
  authenticateMeetUser, 
  formatMeetErrorMessage, 
  handleMeetApiError,
  validatePaginationParams,
  validateDateRange,
  buildMeetFilter,
  formatMeetApiResponse
} from './api-utils'

// Types
export type {
  MeetingSpace,
  ConferenceRecord,
  Participant,
  ParticipantSession,
  Recording,
  Transcript,
  TranscriptEntry,
  CreateSpaceRequest,
  MeetApiContext,
  MeetingAnalytics,
  MeetingInsight,
  MeetEvent,
  EventSubscription,
  MeetApiError,
  ListResponse,
  ConferenceFilter,
  ParticipantFilter,
  RecordingFilter,
  TranscriptFilter
} from './types'

// Meeting Spaces operations
export {
  createMeetingSpace,
  getMeetingSpace,
  updateMeetingSpace,
  endActiveConference,
  getMeetingSpaceAnalytics,
  listMeetingSpaces,
  createCustomMeetingSpace,
  createEventMeetingSpace,
  getMeetingJoinInfo,
  hasActiveConference
} from './spaces'

// Conference operations
export {
  getConferenceRecord,
  listConferenceRecords,
  getSpaceConferenceRecords,
  getRecentConferenceRecords,
  getConferenceStatistics,
  getConferenceDetails,
  searchConferenceRecords
} from './conferences'

// Participant operations
export {
  getParticipant,
  listParticipants,
  getParticipantSession,
  listParticipantSessions,
  getConferenceParticipants,
  getParticipantStatistics,
  getParticipantAttendance,
  searchParticipants
} from './participants'

// Artifacts operations (recordings and transcripts)
export {
  getRecording,
  listRecordings,
  getTranscript,
  listTranscripts,
  getTranscriptEntry,
  listTranscriptEntries,
  getConferenceArtifacts,
  getRecordingDownloadInfo,
  getTranscriptDownloadInfo,
  getFullTranscriptText,
  searchTranscriptEntries,
  getArtifactsSummary
} from './artifacts'

// Events and subscriptions operations
export {
  createEventSubscription,
  getEventSubscription,
  listEventSubscriptions,
  updateEventSubscription,
  deleteEventSubscription,
  reactivateEventSubscription,
  setupCommonMeetSubscriptions,
  getAvailableMeetEventTypes,
  validateEventSubscriptionConfig,
  processMeetEvent
} from './events'

// Convenience functions for common operations
export async function createQuickMeeting(
  userId: string,
  options: {
    accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
    entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
  } = {}
) {
  const { createMeetingSpace } = await import('./spaces')
  
  return await createMeetingSpace(userId, {
    accessType: options.accessType || 'OPEN',
    entryPointAccess: options.entryPointAccess || 'ALL'
  })
}

export async function getMeetingOverview(
  userId: string,
  conferenceRecordName: string
) {
  const [
    { getConferenceDetails },
    { getConferenceParticipants },
    { getConferenceArtifacts }
  ] = await Promise.all([
    import('./conferences'),
    import('./participants'),
    import('./artifacts')
  ])
  
  const [details, participants, artifacts] = await Promise.all([
    getConferenceDetails(userId, conferenceRecordName),
    getConferenceParticipants(userId, conferenceRecordName),
    getConferenceArtifacts(userId, conferenceRecordName)
  ])
  
  return {
    ...details,
    participants: participants.participants,
    totalParticipants: participants.totalParticipants,
    recordings: artifacts.recordings,
    transcripts: artifacts.transcripts,
    hasRecordings: artifacts.hasRecordings,
    hasTranscripts: artifacts.hasTranscripts
  }
}

export async function getDashboardData(
  userId: string,
  days: number = 30
): Promise<{
  recentMeetings: any[]
  statistics: any
  upcomingMeetings: any[]
  totalArtifacts: any
}> {
  const { getRecentConferenceRecords, getConferenceStatistics } = await import('./conferences')
  
  const endTime = new Date().toISOString()
  const startTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
  
  const [recentMeetings, statistics] = await Promise.all([
    getRecentConferenceRecords(userId, days, 10),
    getConferenceStatistics(userId, startTime, endTime)
  ])
  
  return {
    recentMeetings: recentMeetings.items,
    statistics,
    upcomingMeetings: [], // Would integrate with calendar API
    totalArtifacts: {
      recordings: statistics.conferencesWithRecordings,
      transcripts: statistics.conferencesWithTranscripts
    }
  }
}
