// Google Meet API Types

export interface MeetingSpace {
  name: string
  meetingUri: string
  meetingCode: string
  config?: {
    entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
    accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
  }
  activeConference?: {
    conferenceRecord: string
  }
}

export interface ConferenceRecord {
  name: string
  startTime: string
  endTime?: string
  expireTime: string
  space: string
}

export interface Participant {
  name: string
  earliestStartTime: string
  latestEndTime: string
  signalingId: string
  phoneNumber?: string
  anonymousUser?: {
    displayName: string
  }
}

export interface ParticipantSession {
  name: string
  startTime: string
  endTime?: string
  participant: string
}

export interface Recording {
  name: string
  driveDestination: {
    file: string
    exportUri: string
  }
  state: 'STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
}

export interface Transcript {
  name: string
  state: 'STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
  docsDestination: {
    document: string
    exportUri: string
  }
}

export interface TranscriptEntry {
  name: string
  participant: string
  text: string
  languageCode: string
  startTime: string
  endTime: string
}

export interface CreateSpaceRequest {
  config?: {
    entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
    accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
  }
}

export interface MeetApiContext {
  user: {
    id: string
    email: string
    meetRefreshToken: string | null
    meetConnected: boolean
  }
  url: URL
}

export interface MeetingAnalytics {
  totalMeetings: number
  totalParticipants: number
  totalDuration: number // in minutes
  averageDuration: number
  recordingsCount: number
  transcriptsCount: number
  thisWeekMeetings: number
  thisMonthMeetings: number
}

export interface MeetingInsight {
  meetingId: string
  title: string
  duration: number
  participantCount: number
  hasRecording: boolean
  hasTranscript: boolean
  startTime: string
  endTime?: string
  keyTopics?: string[]
  sentiment?: 'positive' | 'neutral' | 'negative'
}

// Event subscription types
export interface MeetEvent {
  eventType: string
  eventTime: string
  meetingSpace?: string
  conferenceRecord?: string
  participant?: string
}

export interface EventSubscription {
  name: string
  targetResource: string
  eventTypes: string[]
  payloadOptions?: {
    includeResource?: boolean
    fieldMask?: string
  }
  notificationEndpoint: {
    pubsubTopic: string
  }
  state: 'STATE_UNSPECIFIED' | 'ACTIVE' | 'SUSPENDED' | 'DELETED'
  createTime: string
  updateTime: string
  expireTime: string
}

// Error types
export interface MeetApiError {
  code: number
  message: string
  details?: any[]
}

// Pagination types
export interface ListResponse<T> {
  items: T[]
  nextPageToken?: string
  totalSize?: number
}

// Filter and query types
export interface ConferenceFilter {
  startTime?: string
  endTime?: string
  space?: string
}

export interface ParticipantFilter {
  conferenceRecord: string
}

export interface RecordingFilter {
  conferenceRecord: string
}

export interface TranscriptFilter {
  conferenceRecord: string
}
