{"name": "eagle-mass-mailer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev && npx prisma migrate dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@google-cloud/local-auth": "^3.0.1", "@google/generative-ai": "^0.24.1", "@heroui/date-picker": "^2.3.22", "@heroui/system": "^2.4.18", "@heroui/theme": "^2.4.17", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "framer-motion": "^12.22.0", "geist": "^1.4.2", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "lucide-react": "^0.523.0", "next": "15.3.2", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-letter": "^0.4.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}