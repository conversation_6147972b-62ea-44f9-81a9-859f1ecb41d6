'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Video, 
  Settings, 
  Activity, 
  Plus,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Calendar
} from 'lucide-react'
import { MeetDashboard } from '@/components/meet/MeetDashboard'
import { CreateMeetingSpace } from '@/components/meet/CreateMeetingSpace'
import { toast } from 'sonner'

interface MeetStatus {
  userId: string
  userEmail: string
  meetConnected: boolean
  hasRefreshToken: boolean
  connectionTest: {
    connected: boolean
    error?: string
  }
  availableFeatures: string[]
}

export default function MeetPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [meetStatus, setMeetStatus] = useState<MeetStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'authenticated') {
      checkMeetStatus()
    }
  }, [status])

  const checkMeetStatus = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/meet/status')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      setMeetStatus(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleConnect = () => {
    router.push('/api/meet/connect')
  }

  const handleRefresh = () => {
    checkMeetStatus()
  }

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading Meet status...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p className="text-muted-foreground">Please sign in to access Google Meet features.</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Connection Error</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  // Show connection screen if Meet is not connected
  if (!meetStatus?.meetConnected) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <Video className="h-16 w-16 text-blue-500 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Connect Google Meet</h2>
          <p className="text-muted-foreground mb-6">
            To use Google Meet features, you need to connect your Google account with Meet permissions.
          </p>
          <Button onClick={handleConnect} className="mb-4">
            <Video className="h-4 w-4 mr-2" />
            Connect Google Meet
          </Button>
          <p className="text-xs text-muted-foreground">
            This will allow you to create and manage meeting spaces, view conference records, and access meeting analytics.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b bg-card px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Video className="h-6 w-6 text-blue-600" />
                <h1 className="text-2xl font-semibold">Google Meet</h1>
              </div>
              <Badge variant="default" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Connected
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Connection Status */}
        {meetStatus?.connectionTest && !meetStatus.connectionTest.connected && (
          <Alert className="m-6 mb-0">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {meetStatus.connectionTest.error || 'Unable to connect to Google Meet API'}
            </AlertDescription>
          </Alert>
        )}

        {/* Available Features */}
        {meetStatus?.availableFeatures && meetStatus.availableFeatures.length > 0 && (
          <div className="px-6 py-4 border-b">
            <h3 className="text-sm font-medium mb-2">Available Features</h3>
            <div className="flex flex-wrap gap-2">
              {meetStatus.availableFeatures.map((feature, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Tabs Content */}
        <div className="flex-1 overflow-hidden p-6">
          <Tabs defaultValue="dashboard" className="h-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dashboard" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="create" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Meeting
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Calendar Integration
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="h-full mt-6">
              <MeetDashboard />
            </TabsContent>

            <TabsContent value="create" className="space-y-4 mt-6">
              <CreateMeetingSpace />
            </TabsContent>

            <TabsContent value="calendar" className="space-y-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Calendar Integration</CardTitle>
                  <CardDescription>
                    Manage how Google Meet integrates with your calendar events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Calendar integration features are available when creating events. 
                    You can automatically create Meet spaces for calendar events with enhanced access controls.
                  </p>
                  <Button className="mt-4" onClick={() => router.push('/dashboard/calendar')}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Go to Calendar
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
