import GoogleProvider from "next-auth/providers/google"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import { NextAuthOptions } from "next-auth"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          scope: "openid email profile https://mail.google.com/ https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/meetings.space.created https://www.googleapis.com/auth/meetings.space.readonly",
          access_type: "offline", // This requests a refresh token
          prompt: "consent", // This forces the consent screen to show, ensuring we get a refresh token
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }: any) {
      return true
    },
    async session({ session, user }: any) {
      if (session.user && user) {
        session.user.id = user.id
        const userData = await prisma.user.findUnique({
          where: { id: user.id },
          select: {
            gmailConnected: true,
            gmailRefreshToken: true,
            calendarConnected: true,
            calendarRefreshToken: true,
            meetConnected: true,
            meetRefreshToken: true
          }
        })
        session.user.gmailConnected = userData?.gmailConnected || false
        session.user.calendarConnected = userData?.calendarConnected || false
        session.user.meetConnected = userData?.meetConnected || false
      }
      return session
    },
    async jwt({ token, user, account }: any) {
      if (account) {
        token.accessToken = account.access_token
        token.refreshToken = account.refresh_token
      }
      return token
    },
  },
  events: {
    async linkAccount({ user, account, profile }: any) {
      // Handle Gmail and Calendar token storage after account is successfully linked
      if (account?.provider === "google") {
        console.log("🔗 Account linked for user:", user.email)
        
        // Check if this Google account has Gmail and Calendar scopes
        const hasGmailScope = account.scope?.includes("https://mail.google.com/")
        const hasCalendarScope = account.scope?.includes("https://www.googleapis.com/auth/calendar")
        
        console.log("🔍 Scope check:", {
          fullScope: account.scope,
          hasGmailScope,
          hasCalendarScope,
          hasRefreshToken: !!account.refresh_token,
          hasAccessToken: !!account.access_token
        })
        
        try {
          // Update user with Gmail and Calendar tokens after account linking
          const updateData: any = {
            dailySendLimit: 250,
            dailySendCount: 0,
            lastSendReset: new Date()
          }
          
          // Handle Gmail connection
          if (hasGmailScope) {
            updateData.gmailConnected = true
            if (account.refresh_token) {
              updateData.gmailRefreshToken = account.refresh_token
              console.log("💾 Storing Gmail refresh token for user:", user.email)
            }
          }
          
          // Handle Calendar connection
          if (hasCalendarScope) {
            updateData.calendarConnected = true
            if (account.refresh_token) {
              updateData.calendarRefreshToken = account.refresh_token
              console.log("📅 Storing Calendar refresh token for user:", user.email)
            }
          }
          
          console.log("🔄 Updating user with data:", updateData)
          
          // Use user ID instead of email for more reliable updates
          const updatedUser = await prisma.user.update({
            where: { id: user.id },
            data: updateData
          })
          
          console.log("✅ User updated successfully:", {
            userId: updatedUser.id,
            gmailConnected: updatedUser.gmailConnected,
            hasGmailToken: !!updatedUser.gmailRefreshToken
          })
          
        } catch (error) {
          console.error("❌ Error saving Google tokens:", error)
        }
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "database" as const,
  },
  debug: process.env.NODE_ENV === "development",
}
